/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "adc.h"
#include "i2c.h"
#include "usart.h"
#include "rtc.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "e70_config.h"
#include <string.h>
#include <stdio.h>
#include "stm32l0xx_hal_pwr.h"
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

// 休眠配置
#define SLEEP_DURATION_SECONDS 10       // 10秒休眠时间
#define LED_BLINK_COUNT 3               // LED闪烁3次
#define LED_BLINK_DELAY 200             // LED闪烁间隔200ms

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */
volatile uint8_t rtcWakeupFlag = 0;  // RTC唤醒标志
uint32_t wakeup_counter = 0;         // 唤醒计数器
/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */
void BlinkLED(uint8_t count, uint32_t delay);
void EnterSleepMode(void);
void DisableAllPeripherals(void);
/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{

  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_I2C1_Init();
  MX_LPUART1_UART_Init();
  MX_USART2_UART_Init();
  MX_ADC_Init();
  MX_RTC_Init();
  /* USER CODE BEGIN 2 */
	printf("=== STM32L031G6 Sleep Test Start ===\r\n");
	printf("10秒休眠自动唤醒LED闪烁测试\r\n");
	printf("测试所有外设关闭后的休眠电流\r\n\r\n");

	// 初始化完成，LED闪烁一次表示系统启动
	BlinkLED(1, 500);
	HAL_Delay(1000);

  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */
    wakeup_counter++;
    printf("=== 唤醒周期 #%lu ===\r\n", wakeup_counter);

    // 唤醒后LED闪烁3次
    printf("LED闪烁3次...\r\n");
    BlinkLED(LED_BLINK_COUNT, LED_BLINK_DELAY);

    printf("关闭所有外设电源...\r\n");
    DisableAllPeripherals();

    printf("进入%d秒休眠模式...\r\n", SLEEP_DURATION_SECONDS);
    HAL_Delay(100); // 确保printf输出完成

    // 进入休眠模式
    EnterSleepMode();

    // 唤醒后会从这里继续执行
    printf("从休眠模式唤醒！\r\n\r\n");

  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};
  RCC_PeriphCLKInitTypeDef PeriphClkInit = {0};

  /** Configure the main internal regulator output voltage
  */
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_LSI|RCC_OSCILLATORTYPE_MSI;
  RCC_OscInitStruct.LSIState = RCC_LSI_ON;
  RCC_OscInitStruct.MSIState = RCC_MSI_ON;
  RCC_OscInitStruct.MSICalibrationValue = 0;
  RCC_OscInitStruct.MSIClockRange = RCC_MSIRANGE_5;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_NONE;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_MSI;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV1;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_0) != HAL_OK)
  {
    Error_Handler();
  }
  PeriphClkInit.PeriphClockSelection = RCC_PERIPHCLK_USART2|RCC_PERIPHCLK_LPUART1
                              |RCC_PERIPHCLK_I2C1|RCC_PERIPHCLK_RTC;
  PeriphClkInit.Usart2ClockSelection = RCC_USART2CLKSOURCE_PCLK1;
  PeriphClkInit.Lpuart1ClockSelection = RCC_LPUART1CLKSOURCE_PCLK1;
  PeriphClkInit.I2c1ClockSelection = RCC_I2C1CLKSOURCE_PCLK1;
  PeriphClkInit.RTCClockSelection = RCC_RTCCLKSOURCE_LSI;
  if (HAL_RCCEx_PeriphCLKConfig(&PeriphClkInit) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */

/**
 * @brief LED闪烁函数
 * @param count: 闪烁次数
 * @param delay: 每次闪烁的延时(ms)
 */
void BlinkLED(uint8_t count, uint32_t delay)
{
  for (uint8_t i = 0; i < count; i++)
  {
    LED_ON;
    HAL_Delay(delay);
    LED_OFF;
    HAL_Delay(delay);
  }
}

/**
 * @brief 关闭所有外设电源以降低功耗
 */
void DisableAllPeripherals(void)
{
  // 关闭所有外设电源
  RF_PWR_OFF;           // 关闭RF模块电源
  MLX90393_PW_OFF;      // 关闭MLX90393传感器电源

  // 关闭所有GPIO为低电平以降低功耗
  HAL_GPIO_WritePin(GPIOA, M0_Pin|M1_Pin|M2_Pin, GPIO_PIN_RESET);

  // 关闭外设时钟以节省功耗
  __HAL_RCC_I2C1_CLK_DISABLE();
  __HAL_RCC_LPUART1_CLK_DISABLE();
  __HAL_RCC_USART2_CLK_DISABLE();
  __HAL_RCC_ADC1_CLK_DISABLE();
}

/**
 * @brief 进入STOP模式休眠
 */
void EnterSleepMode(void)
{
  // 关闭LED
  LED_OFF;

  // 确保UART传输完成
  while(__HAL_UART_GET_FLAG(&hlpuart1, UART_FLAG_TC) == RESET);
  while(__HAL_UART_GET_FLAG(&huart2, UART_FLAG_TC) == RESET);
  HAL_Delay(10);

  // 配置RTC唤醒定时器
  HAL_RTCEx_DeactivateWakeUpTimer(&hrtc);

  // 清除任何挂起的标志
  __HAL_RTC_WAKEUPTIMER_CLEAR_FLAG(&hrtc, RTC_FLAG_WUTF);
  __HAL_RTC_WAKEUPTIMER_EXTI_CLEAR_FLAG();

  // 设置唤醒定时器 (使用1Hz时钟源，计数器值 = 秒数 - 1)
  uint32_t wakeup_time = SLEEP_DURATION_SECONDS - 1;
  if (HAL_RTCEx_SetWakeUpTimer_IT(&hrtc, wakeup_time, RTC_WAKEUPCLOCK_CK_SPRE_16BITS) != HAL_OK) {
    printf("Failed to set wakeup timer\r\n");
    return;
  }

  // 启用RTC中断
  HAL_NVIC_SetPriority(RTC_IRQn, 0, 0);
  HAL_NVIC_EnableIRQ(RTC_IRQn);

  // 启用RTC唤醒的EXTI
  __HAL_RTC_WAKEUPTIMER_EXTI_ENABLE_IT();
  __HAL_RTC_WAKEUPTIMER_EXTI_ENABLE_RISING_EDGE();

  // 进入STOP模式
  HAL_PWR_EnterSTOPMode(PWR_LOWPOWERREGULATOR_ON, PWR_STOPENTRY_WFI);

  // 唤醒后重新配置系统时钟
  SystemClock_Config();

  // 重新初始化必要的外设时钟
  __HAL_RCC_LPUART1_CLK_ENABLE();
  __HAL_RCC_USART2_CLK_ENABLE();

  // 重新初始化UART
  MX_LPUART1_UART_Init();
  MX_USART2_UART_Init();

  // 停止唤醒定时器
  HAL_RTCEx_DeactivateWakeUpTimer(&hrtc);

  // 清除唤醒标志
  rtcWakeupFlag = 0;
}

/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}
#ifdef USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
