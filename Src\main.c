/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "adc.h"
#include "i2c.h"
#include "usart.h"
#include "rtc.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "e70_config.h"
#include <string.h>
#include <stdio.h>
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{

  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_I2C1_Init();
  MX_LPUART1_UART_Init();
  MX_USART2_UART_Init();
  MX_ADC_Init();
  MX_RTC_Init();
  /* USER CODE BEGIN 2 */
	printf("=== E70 Module Test Start ===\r\n");

	// 进入配置模式5 (101) - 这是有效的配置模式
	E70_SetModeWithPowerCycle(E70_MODE_CONFIG5);
	E70_Init();

	printf("=== Basic E70 Communication Test ===\r\n");

	// 测试1：读取工作参数
	printf("Test 1: Reading work parameters (C1 C1 C1)\r\n");
	E70_StartRx();
	uint8_t cmd_cfg[] = {0xC1, 0xC1, 0xC1};
	HAL_UART_Transmit(&hlpuart1, cmd_cfg, 3, 1000);
	HAL_Delay(1000);
	E70_StopRx();
	E70_PrintRxBuffer();
	E70_ParseConfigData();

	// 测试2：读取版本信息
	printf("\r\nTest 2: Reading version info (C3 C3 C3)\r\n");
	E70_StartRx();
	uint8_t cmd_ver[] = {0xC3, 0xC3, 0xC3};
	HAL_UART_Transmit(&hlpuart1, cmd_ver, 3, 1000);
	HAL_Delay(2000);  // 增加等待时间
	E70_StopRx();
	E70_PrintRxBuffer();
	E70_ParseConfigData();

	// 测试3：尝试复位命令
	printf("\r\nTest 3: Reset command (C4 C4 C4)\r\n");
	E70_StartRx();
	uint8_t cmd_reset[] = {0xC4, 0xC4, 0xC4};
	HAL_UART_Transmit(&hlpuart1, cmd_reset, 3, 1000);
	HAL_Delay(1000);
	E70_StopRx();
	E70_PrintRxBuffer();

	// 测试4：再次读取工作参数确认通信稳定
	printf("\r\nTest 4: Re-read work parameters (C1 C1 C1)\r\n");
	E70_StartRx();
	HAL_UART_Transmit(&hlpuart1, cmd_cfg, 3, 1000);
	HAL_Delay(1000);
	E70_StopRx();
	E70_PrintRxBuffer();
	E70_ParseConfigData();

	// 测试5：写入新配置
	printf("\r\nTest 5: Writing new configuration\r\n");
	uint8_t write_success = E70_WriteSimpleConfig(0x1234, 10, 1);  // 地址0x1234, 信道10, 10dBm功率

	if (write_success) {
		// 验证写入结果
		printf("\r\nVerifying new configuration:\r\n");
		E70_StartRx();
		HAL_UART_Transmit(&hlpuart1, cmd_cfg, 3, 1000);
		HAL_Delay(1000);
		E70_StopRx();
		E70_PrintRxBuffer();
		E70_ParseConfigData();
	}

	printf("\r\n=== E70 Communication Test Complete ===\r\n");
	printf("✅ E70 module is responding to configuration commands!\r\n");
	printf("✅ Basic read/write operations are working!\r\n");

  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */
			HAL_GPIO_WritePin(GPIOA, M0_Pin|M1_Pin|M2_Pin|MLX90393_PW_Pin, GPIO_PIN_RESET);
      RF_PWR_OFF;
//    LED_TOGGLE;
//    HAL_Delay(200);

  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};
  RCC_PeriphCLKInitTypeDef PeriphClkInit = {0};

  /** Configure the main internal regulator output voltage
  */
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_LSI|RCC_OSCILLATORTYPE_MSI;
  RCC_OscInitStruct.LSIState = RCC_LSI_ON;
  RCC_OscInitStruct.MSIState = RCC_MSI_ON;
  RCC_OscInitStruct.MSICalibrationValue = 0;
  RCC_OscInitStruct.MSIClockRange = RCC_MSIRANGE_5;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_NONE;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_MSI;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV1;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_0) != HAL_OK)
  {
    Error_Handler();
  }
  PeriphClkInit.PeriphClockSelection = RCC_PERIPHCLK_USART2|RCC_PERIPHCLK_LPUART1
                              |RCC_PERIPHCLK_I2C1|RCC_PERIPHCLK_RTC;
  PeriphClkInit.Usart2ClockSelection = RCC_USART2CLKSOURCE_PCLK1;
  PeriphClkInit.Lpuart1ClockSelection = RCC_LPUART1CLKSOURCE_PCLK1;
  PeriphClkInit.I2c1ClockSelection = RCC_I2C1CLKSOURCE_PCLK1;
  PeriphClkInit.RTCClockSelection = RCC_RTCCLKSOURCE_LSI;
  if (HAL_RCCEx_PeriphCLKConfig(&PeriphClkInit) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */

/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}
#ifdef USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
