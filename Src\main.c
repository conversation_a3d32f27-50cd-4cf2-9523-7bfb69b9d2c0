/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "adc.h"
#include "i2c.h"
#include "usart.h"
#include "rtc.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "e70_config.h"
#include <string.h>
#include <stdio.h>
#include "stm32l0xx_hal_pwr.h"
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

// 休眠配置参数
#define SLEEP_DURATION_SECONDS 10       // 10秒休眠时间
#define LED_BLINK_COUNT 3               // LED闪烁3次
#define LED_BLINK_DELAY 200             // LED闪烁间隔200ms

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */
volatile uint8_t rtcWakeupFlag = 0;  // RTC唤醒标志
uint32_t wakeup_counter = 0;         // 唤醒计数器
/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */
void BlinkLED(uint8_t count, uint32_t delay);      // LED闪烁函数
void EnterSleepMode(void);                         // 进入休眠模式函数
void DisableAllPeripherals(void);                  // 关闭所有外设函数
/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{

  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_I2C1_Init();
  MX_LPUART1_UART_Init();
  MX_USART2_UART_Init();
  MX_ADC_Init();
  MX_RTC_Init();
  /* USER CODE BEGIN 2 */
	printf("=== STM32L031G6 Sleep Test Start ===\r\n");
	printf("10s sleep auto wakeup LED blink test\r\n");
	printf("Test sleep current with all peripherals off\r\n\r\n");

	// 初始化完成，LED闪烁一次表示系统启动
	BlinkLED(1, 500);
	HAL_Delay(1000);

  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */
    wakeup_counter++;
    printf("=== Wakeup Cycle #%lu ===\r\n", wakeup_counter);

    // 唤醒后LED闪烁3次
    printf("LED blinking %d times...\r\n", LED_BLINK_COUNT);
    BlinkLED(LED_BLINK_COUNT, LED_BLINK_DELAY);

    printf("Disabling all peripherals...\r\n");
    DisableAllPeripherals();

    printf("Entering %d seconds sleep mode...\r\n", SLEEP_DURATION_SECONDS);
    HAL_Delay(100); // 确保printf输出完成

    // 进入休眠模式
    EnterSleepMode();

    // 唤醒后会从这里继续执行
    printf("Woke up from sleep mode!\r\n\r\n");

  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};
  RCC_PeriphCLKInitTypeDef PeriphClkInit = {0};

  /** Configure the main internal regulator output voltage
  */
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_LSI|RCC_OSCILLATORTYPE_MSI;
  RCC_OscInitStruct.LSIState = RCC_LSI_ON;
  RCC_OscInitStruct.MSIState = RCC_MSI_ON;
  RCC_OscInitStruct.MSICalibrationValue = 0;
  RCC_OscInitStruct.MSIClockRange = RCC_MSIRANGE_5;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_NONE;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_MSI;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV1;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_0) != HAL_OK)
  {
    Error_Handler();
  }
  PeriphClkInit.PeriphClockSelection = RCC_PERIPHCLK_USART2|RCC_PERIPHCLK_LPUART1
                              |RCC_PERIPHCLK_I2C1|RCC_PERIPHCLK_RTC;
  PeriphClkInit.Usart2ClockSelection = RCC_USART2CLKSOURCE_PCLK1;
  PeriphClkInit.Lpuart1ClockSelection = RCC_LPUART1CLKSOURCE_PCLK1;
  PeriphClkInit.I2c1ClockSelection = RCC_I2C1CLKSOURCE_PCLK1;
  PeriphClkInit.RTCClockSelection = RCC_RTCCLKSOURCE_LSI;
  if (HAL_RCCEx_PeriphCLKConfig(&PeriphClkInit) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */

/**
 * @brief LED闪烁函数
 * @param count: 闪烁次数
 * @param delay: 每次闪烁的延时(ms)
 */
void BlinkLED(uint8_t count, uint32_t delay)
{
  for (uint8_t i = 0; i < count; i++)
  {
    LED_ON;
    HAL_Delay(delay);
    LED_OFF;
    HAL_Delay(delay);
  }
}

/**
 * @brief 关闭所有外设电源以降低功耗
 */
void DisableAllPeripherals(void)
{
  // 停止ADC和DMA
  HAL_ADC_Stop_DMA(&hadc);
  HAL_ADC_DeInit(&hadc);

  // 停止串口接收中断
  HAL_UART_AbortReceive_IT(&hlpuart1);
  HAL_UART_AbortReceive_IT(&huart2);

  // 关闭所有外设电源
  RF_PWR_OFF;           // 关闭RF模块电源
  MLX90393_PW_OFF;      // 关闭MLX90393传感器电源

  // 关闭所有GPIO为低电平以降低功耗
  HAL_GPIO_WritePin(GPIOA, M0_Pin|M1_Pin|M2_Pin, GPIO_PIN_RESET);

  // 关闭外设时钟以节省功耗
  __HAL_RCC_I2C1_CLK_DISABLE();
  __HAL_RCC_LPUART1_CLK_DISABLE();
  __HAL_RCC_USART2_CLK_DISABLE();
  __HAL_RCC_ADC1_CLK_DISABLE();
  __HAL_RCC_DMA1_CLK_DISABLE();
}

/**
 * @brief 进入STOP模式休眠 (参考ADC示例的正确实现)
 */
void EnterSleepMode(void)
{
  // 关闭LED
  LED_OFF;

  // 停止ADC DMA
  HAL_ADC_Stop_DMA(&hadc);

  // 停止UART接收中断
  HAL_UART_AbortReceive_IT(&hlpuart1);
  HAL_UART_AbortReceive_IT(&huart2);

  // 禁用所有外设中断
  HAL_NVIC_DisableIRQ(LPUART1_IRQn);
  HAL_NVIC_DisableIRQ(USART2_IRQn);
  HAL_NVIC_DisableIRQ(ADC1_COMP_IRQn);
  HAL_NVIC_DisableIRQ(DMA1_Channel1_IRQn);
  HAL_NVIC_DisableIRQ(I2C1_IRQn);

  // 确保UART传输完成
  while(__HAL_UART_GET_FLAG(&hlpuart1, UART_FLAG_TC) == RESET);
  while(__HAL_UART_GET_FLAG(&huart2, UART_FLAG_TC) == RESET);
  HAL_Delay(10);  // 使用HAL_Delay而不是osDelay，因为即将暂停调度器

  // 配置RTC唤醒定时器

  // 确保所有中断被处理
  __disable_irq();

  // 取消激活任何现有的唤醒定时器
  HAL_RTCEx_DeactivateWakeUpTimer(&hrtc);

  // 清除任何挂起的标志
  __HAL_RTC_WAKEUPTIMER_CLEAR_FLAG(&hrtc, RTC_FLAG_WUTF);
  __HAL_RTC_WAKEUPTIMER_EXTI_CLEAR_FLAG();

  // 设置唤醒定时器，使用RTC_WAKEUPCLOCK_CK_SPRE_16BITS（1Hz时钟源）
  // 休眠时间 = 计数器 / 1Hz，所以计数器 = 休眠秒数 - 1
  uint32_t wakeup_time = SLEEP_DURATION_SECONDS - 1;
  if (HAL_RTCEx_SetWakeUpTimer_IT(&hrtc, wakeup_time, RTC_WAKEUPCLOCK_CK_SPRE_16BITS) != HAL_OK) {
    printf("Failed to set wakeup timer\r\n");
    __enable_irq();
    return;
  }

  // 启用RTC中断，使用最高优先级
  HAL_NVIC_SetPriority(RTC_IRQn, 0, 0);
  HAL_NVIC_EnableIRQ(RTC_IRQn);

  // 启用RTC唤醒的EXTI
  __HAL_RTC_WAKEUPTIMER_EXTI_ENABLE_IT();
  __HAL_RTC_WAKEUPTIMER_EXTI_ENABLE_RISING_EDGE();

  __enable_irq();

  // 获取当前时间
  RTC_TimeTypeDef time_before;
  RTC_DateTypeDef date_before;
  HAL_RTC_GetTime(&hrtc, &time_before, RTC_FORMAT_BIN);
  HAL_RTC_GetDate(&hrtc, &date_before, RTC_FORMAT_BIN);

  printf("Time before sleep: %02d:%02d:%02d\r\n",
         time_before.Hours, time_before.Minutes, time_before.Seconds);

  // 确保消息发送完毕
  while(__HAL_UART_GET_FLAG(&hlpuart1, UART_FLAG_TC) == RESET);
  while(__HAL_UART_GET_FLAG(&huart2, UART_FLAG_TC) == RESET);
  HAL_Delay(10);  // 使用HAL_Delay而不是osDelay，因为即将禁用中断

  // 禁用所有中断，准备进入低功耗模式
  __disable_irq();

  // 禁用SysTick中断
  SysTick->CTRL &= ~SysTick_CTRL_TICKINT_Msk;

  // 禁用所有中断，只保留RTC唤醒中断
  for (uint8_t i = 0; i < 8; i++) {
    NVIC->ICER[i] = 0xFFFFFFFF;
  }

  // 确保RTC唤醒中断被启用
  HAL_NVIC_SetPriority(RTC_IRQn, 0, 0);
  HAL_NVIC_EnableIRQ(RTC_IRQn);

  // 清除所有挂起的中断
  for (uint8_t i = 0; i < 8; i++) {
    NVIC->ICPR[i] = 0xFFFFFFFF;
  }

  // 重新启用全局中断，但只有RTC中断处于活动状态
  __enable_irq();

  // 进入STOP模式，使用低功耗调节器
  HAL_PWR_EnterSTOPMode(PWR_LOWPOWERREGULATOR_ON, PWR_STOPENTRY_WFI);

  // 唤醒后 - 以下代码在唤醒后执行

  // 打开LED表示唤醒开始
  LED_ON;

  // 重新配置系统时钟 - STOP模式后必需
  SystemClock_Config();

  // 重新初始化所有必要的外设时钟
  __HAL_RCC_LPUART1_CLK_ENABLE();
  __HAL_RCC_USART2_CLK_ENABLE();
  __HAL_RCC_I2C1_CLK_ENABLE();
  __HAL_RCC_ADC1_CLK_ENABLE();
  __HAL_RCC_DMA1_CLK_ENABLE();

  // 重新初始化外设
  MX_LPUART1_UART_Init();
  MX_USART2_UART_Init();
  MX_I2C1_Init();
  MX_ADC_Init();

  // 重新启用所有外设中断
  HAL_NVIC_SetPriority(LPUART1_IRQn, 3, 0);
  HAL_NVIC_EnableIRQ(LPUART1_IRQn);
  HAL_NVIC_SetPriority(USART2_IRQn, 3, 0);
  HAL_NVIC_EnableIRQ(USART2_IRQn);
  HAL_NVIC_SetPriority(ADC1_COMP_IRQn, 3, 0);
  HAL_NVIC_EnableIRQ(ADC1_COMP_IRQn);
  HAL_NVIC_SetPriority(DMA1_Channel1_IRQn, 3, 0);
  HAL_NVIC_EnableIRQ(DMA1_Channel1_IRQn);
  HAL_NVIC_SetPriority(I2C1_IRQn, 3, 0);
  HAL_NVIC_EnableIRQ(I2C1_IRQn);

  // 重新启用SysTick中断
  SysTick->CTRL |= SysTick_CTRL_TICKINT_Msk;

  // 重新启用全局中断
  __enable_irq();

  // 获取当前时间
  RTC_TimeTypeDef time_after;
  RTC_DateTypeDef date_after;
  HAL_RTC_GetTime(&hrtc, &time_after, RTC_FORMAT_BIN);
  HAL_RTC_GetDate(&hrtc, &date_after, RTC_FORMAT_BIN);

  // 停止唤醒定时器
  HAL_RTCEx_DeactivateWakeUpTimer(&hrtc);

  printf("Time after sleep: %02d:%02d:%02d\r\n",
         time_after.Hours, time_after.Minutes, time_after.Seconds);

  // 计算经过的时间
  int elapsed_seconds = (time_after.Hours - time_before.Hours) * 3600 +
                        (time_after.Minutes - time_before.Minutes) * 60 +
                        (time_after.Seconds - time_before.Seconds);
  if (elapsed_seconds < 0) {
    elapsed_seconds += 24 * 3600; // 处理跨天情况
  }

  printf("Elapsed time: %d seconds\r\n", elapsed_seconds);

  // 清除唤醒标志
  rtcWakeupFlag = 0;
}

/**
 * @brief RTC唤醒定时器事件回调
 */
void HAL_RTCEx_WakeUpTimerEventCallback(RTC_HandleTypeDef *hrtc)
{
  // 设置唤醒标志
  rtcWakeupFlag = 1;

  // 清除RTC唤醒标志
  __HAL_RTC_WAKEUPTIMER_CLEAR_FLAG(hrtc, RTC_FLAG_WUTF);

  // 清除EXTI线标志
  __HAL_RTC_WAKEUPTIMER_EXTI_CLEAR_FLAG();

  // 注意：这里不要使用printf，因为从中断上下文调用可能导致系统崩溃
}

/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}
#ifdef USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
